# 互助会会員システム プロジェクト計画書

## 1. プロジェクト概要

### 1.1 背景と目的
- **背景**: 数十万会員を抱える現行システムの老朽化と機能拡張の必要性
- **目的**:
  - 新規パッケージシステムの開発・販売
  - 業務効率化と顧客体験向上
  - セキュリティ強化と拡張可能なアーキテクチャ構築

### 1.2 プロジェクト範囲
- **対象システム**: 互助会会員管理システムのリプレース
- **対象範囲**: 会員管理機能の開発・改善
- **非対象範囲**: 外部連携システムの改修（インターフェースのみ対応）

### 1.3 主要成果物
- 新規パッケージシステム（ソースコード含む）
- システム設計書一式
- 操作・運用マニュアル

## 2. プロジェクト体制

### 2.1 組織構成
- **親会社側**:
  - プロジェクトスポンサー（経営層）
  - プロジェクト責任者
  - 業務責任者
  - 業務担当者
  - IT責任者
- **子会社側**:
  - プロジェクトマネージャー(PM)
  - プロジェクトリーダー(PL)
  - システムエンジニア(SE)
  - プログラマー（PG）
  - 営業担当者

### 2.2 役割と責任
| 役割 | 責任範囲 | 関与度 |
|------|----------|--------|
| プロジェクトスポンサー | 予算承認・重要意思決定 | 高 |
| プロジェクト責任者 | プロジェクトの意思決定 | 高 |
| 業務責任者 | 業務要件の決定 | 高 |
| 業務担当者 | 業務要件の提供 | 高 |
| IT責任者 | 導入全般の支援 | 高 |
| プロジェクトマネージャー | プロジェクト統括 | 高 |
| プロジェクトリーダー | スコープ管理・スケジュール管理 | 高 |
| システムエンジニア | ヒアリング・システム設計 | 高 |
| プログラマー | コード開発 | 低 |
| 営業担当者 | 営業・折衝管理 | 中 |

## 3. プロジェクト計画

### 3.1 全体スケジュール
```mermaid
gantt
    title 互助会会員システムリプレース 全体スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m

    section 1: 現行調査
    現行調査 :survey, 2025-09-01, 2026-01-31

    section 2: RFP作成
    RFP作成 : rfp, 2026-02-01, 2026-04-30

    section 3: 提案
    提案 : prop, 2026-05-01, 2026-06-15

    section 4: 要件定義
    要件定義 : req, 2026-06-16, 2026-11-15

    section 5: 設計・開発・テスト
    設計・開発・テスト : design, 2026-11-16, 2028-03-31

    section 6: 受入テスト
    検証・受入 : test, 2028-04-01, 2028-08-31

    section 7: 移行・稼働
    移行・稼働 : migrate, 2028-09-01, 2028-12-31
```
### 3.2 主要マイルストーン
| マイルストーン | 開始予定日 | 終了予定日 | 期間 | 成果物 |
|---------------|------------|------------|------|--------|
| 現行調査 | 2025-09-01 | 2026-01-31 | 5ヶ月 | 現行調査報告書 |
| RFP作成 | 2026-02-01 | 2026-04-30 | 3ヶ月 | RFP・システム要件書 |
| 提案 | 2026-05-01 | 2026-06-15 | 1.5ヶ月 | 見積書一式 |
| 要件定義 | 2026-06-16 | 2026-11-15 | 5ヶ月 | 要件定義書一式 |
| 基本設計 | 2026-11-16 | 2027-04-15 | 5ヶ月 | 基本設計書一式 |
| 詳細・開発・単体 | 2027-04-16 | 2027-11-15 | 7ヶ月 | プログラム一式 |
| システムテスト | 2027-11-16 | 2028-03-31 | 4.5ヶ月 | テスト結果一式|
| 研修・受入テスト | 2028-04-01 | 2028-08-31 | 5ヶ月 | 研修資料一式 |
| データ移行 | 2028-04-01 | 2028-08-31 | 5ヶ月 | コンバートデータ一式 |
| 本番稼働開始 | 2028-09-01 |  |  |  |

## 4. リソース計画

### 4.1 人員計画
- **総工数**: 142人月
- **ピーク時体制**: PM1名 + PL1名 + SE3名 + PG5名 + 営業1名

#### 4.2.1 イニシャル予算（初期開発費用）
- **イニシャル総予算**: 127,800,000円（人月単価900,000円で計算）
- **内訳**:
  - 現行調査: 9,000,000円　(10人月)
  - RFP作成: 5,400,000円　(6人月)
  - 提案: 900,000円　(1人月)
  - 要件定義: 9,000,000円　(10人月)
  - 基本設計: 9,000,000円　(10人月)
  - 詳細・開発・単体: 50,400,000円　(56人月)
  - システムテスト: 16,200,000円　(18人月)
  - 受入テスト: 9,000,000円　(10人月)
  - 移行: 7,200,000円　(8人月)
  - 管理: 11,700,000円　(13人月)

#### 4.2.2 ランニング予算（運用保守費用）
- **年間ランニング費用**: 14,220,000円
- **5年間総額**: 71,100,000円

**ランニング費用内訳（年額）**:
- システム保守費用: 12,420,000円
  - 基準額: 82,800,000円（基本設計+詳細・開発・単体+システムテスト+移行の合計）
  - 保守率: 15%（82,800,000円 × 15% = 12,420,000円）
- サーバ使用料: 1,800,000円（月額150,000円 × 12ヶ月）

**5年間ランニング予算**:
| 年度 | システム保守費用 | サーバ使用料 | 年間合計 |
|------|------------------|--------------|----------|
| 1年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 2年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 3年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 4年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 5年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| **合計** | **62,100,000円** | **9,000,000円** | **71,100,000円** |

#### 4.2.3 総予算（イニシャル + ランニング5年分）
- **総予算**: 198,900,000円
  - イニシャル予算: 127,800,000円
  - ランニング予算（5年分）: 71,100,000円

## 5. フェーズ別リソース計画

| フェーズ | 期間 | 担当者数 | 総人月 |
|---------|------|----------|--------|
| 1. 現行調査・分析 | 5ヶ月 | SE2名 | 10人月 |
| 2. RFP作成・コンサルティング | 3ヶ月 | SE2名 | 6人月 |
| 3. 提案書作成・提案 | 1.5ヶ月 | SE1名 | 1人月 |
| 4. 要件定義 | 5ヶ月 | SE2名 | 10人月 |
| 5. 基本設計 | 5ヶ月 | SE2名 | 10人月 |
| 6. 詳細設計・開発・単体テスト | 7ヶ月 | SE3名+PG5名 | 56人月 |
| 7. システムテスト | 4.5ヶ月 | SE2名+PG2名 | 18人月 |
| 8. 受入対応 | 5ヶ月 | SE2名+PG2名 | 10人月 |
| 9. 移行・稼働 | 5ヶ月 | SE1名+PG1名 | 8人月 |
| 10. プロジェクト管理 | 全期間 | PM1名+PL1名 | 13人月 |
| **合計** | 約36ヶ月 | - | **142人月** |

## 6. 品質管理計画

### 6.1 IDCFクラウド品質基準

#### 6.1.1 可用性基準
- **SLA**: 99.9%（月間ダウンタイム43分以内）
- **構成**: IDCFクラウドのマルチAZ構成検討
- **冗長化**: ストレージRAID構成による冗長化

#### 6.1.2 性能基準
- **レスポンスタイム**:
  - 通常処理: 3秒以内（90%ile）
  - 検索処理: 3秒以内（90%ile）
  - 帳票出力: 10秒以内（90%ile）
- **スループット**: 500TPS以上（ピーク時）
- **同時接続数**: 500ユーザー（ピーク時）

#### 6.1.3 セキュリティ基準
- **ネットワークセキュリティ**:
  - IDCFクラウドのファイアウォール機能
  - WAF（Web Application Firewall）検討
  - VPN接続による専用線アクセス検討
  - クライアント認証機能
  - IP制限機能
- **データセキュリティ**:
  - SSL/TLS暗号化通信（TLS1.2以上）
  - パスワード暗号化
- **アクセス制御**:
  - ロールベースアクセス制御（RBAC）
  - 操作ログの記録・監査

#### 6.1.4 運用基準
- **監視体制**: 24時間365日監視
- **バックアップ**:
  - 日次フルバックアップ
  - 遠隔地への複製保存
- **災害対策**:
  - RTO（目標復旧時間）: 4時間以内
  - RPO（目標復旧ポイント）: 1時間以内

### 6.2 品質ゲート
| ゲート | 時期 | チェック項目 |
|--------|------|--------------|
| 要件定義完了 | 2026-11 | 業務/システム要件の合意 |
| 基本設計完了 | 2027-04 | 基本設計書の承認 |
| 開発完了 | 2028-04 | テスト結果確認 |

## 7. ステークホルダー管理

### 7.1 顧客側ステークホルダー
| 役割 | 責任範囲 | 関与度 | 主要タスク |
|------|----------|--------|------------|
| 経営責任者 | 予算承認・重要意思決定 | 高 | 月次進捗確認・重要判断 |
| 業務責任者 | 業務要件の決定 | 高 | 要件定義・受入テスト |
| 業務担当者 | 業務要件の提供 | 高 | 要件定義・動作確認・操作性評価 |
| IT責任者 | 導入全般の支援 | 高 | 導入全般の支援・運用マニュアル作成 |

### 7.2 ベンダー側ステークホルダー
| 役割 | 責任範囲 | 関与度 | 主要タスク |
|------|----------|--------|------------|
| プロジェクトマネージャー | 全体統括 | 高 | 進捗管理・リスク管理 |
| 開発リーダー | 開発工程管理 | 高 | 開発チーム統括 |
| 営業担当 | 顧客折衝 | 中 | 契約・調整業務 |

## 8. リスク管理

### 8.1 技術的リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| 現行システム情報不足 | 中 | 高 | 早期の現行システム会社との調整 |
| IDCFクラウド性能不足 | 低 | 中 | 事前性能検証・スケールアップ対応 |
| セキュリティ要件未達 | 低 | 高 | セキュリティ専門家の早期アサイン |

### 8.2 プロジェクト管理リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| スケジュール遅延 | 中 | 高 | クリティカルパスのバッファ確保 |
| 品質問題 | 中 | 中 | 段階的な品質ゲートの設置 |
| 要件変更 | 高 | 中 | 変更管理プロセスの厳格化 |

### 8.3 移行リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| データ移行失敗 | 中 | 高 | 十分なリハーサル期間の確保 |
| 業務継続性問題 | 低 | 高 | 並行稼働期間の設定 |
| ユーザー受入れ問題 | 中 | 中 | 十分な教育・研修期間の確保 |

## 9. コミュニケーション計画

### 9.1 会議体制
- **ステアリングコミッティ**: 月次（経営層向け）
- **プロジェクト進捗会議**: 週次（実務者向け）
- **技術レビュー**: 随時（設計・開発フェーズ）

### 9.2 報告体制
- 進捗報告: 週次ミーティング
- 課題管理: Redmineによるトラッキング

## 10. 変更管理

### 10.1 変更管理プロセス
1. 変更要求の提出
2. 影響度分析
3. 承認プロセス
4. 実装と検証

## 11. プロジェクト管理ツール
- **進捗管理**: Googleスプレッドシート
- **タスク・課題管理**: Redmine
- **構成管理**: Subversion

---

**承認欄**
| 役職                 | 氏名 | 署名 | 承認日 |
|----------------------|------|------|--------|
| プロジェクトスポンサー |      |      |        |
| プロジェクト責任者     |      |      |        |
| 業務責任者             |      |      |        |
| IT責任者               |      |      |        |
| プロジェクトマネージャー |      |      |        |
| プロジェクトリーダー   |      |      |        |
